"use client";

import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Font,
} from "@react-pdf/renderer";

interface Grant {
  id: number;
  grant_name: string;
  grant_purpose: string;
  start_date: string;
  end_date: string;
  annual_budget: number;
  funding_sources: string;
}

interface QuestionAnswer {
  id: number;
  question: string;
  answer: string;
}

interface Organization {
  id: number;
  organization_name: string;
  organization_legal_type_name: string;
  organization_function_type_name: string;
  pan_number: string;
  phone_number: string;
  email_address: string;
  website_url: string;
}

interface NarrativeReport {
  id: number;
  status: "APPROVED" | "PENDING" | "REJECTED";
  quarter: number;
  year: number;
  created_at: string;
  rejection_remarks?: string;
  remarks?: string;
  grant: Grant;
  question_answers: QuestionAnswer[];
  organization: Organization;
}

const styles = StyleSheet.create({
  page: {
    padding: 40,
    fontSize: 12,
    fontFamily: "Helvetica",
  },
  section: {
    marginBottom: 20,
  },
  heading: {
    fontSize: 18,
    marginBottom: 10,
    fontWeight: "bold",
  },
  paragraph: {
    fontSize: 12,
    marginBottom: 5,
  },
  label: {
    fontWeight: "bold",
    marginBottom: 5,
  },
  value: {
    marginBottom: 6,
  },
  qa: {
    marginBottom: 12,
  },
});

export default function NarrativeReportPdf({
  report,
}: {
  report: NarrativeReport;
}) {
  return (
    <Document>
      <Page style={styles.page}>
        <View style={styles.section}>
          <Text style={styles.heading}>Narrative Report</Text>
          <Text style={styles.paragraph}>Report ID: {report.id}</Text>
          <Text style={styles.paragraph}>
            Quarter: Q{report.quarter} {report.year}
          </Text>
          <Text style={styles.paragraph}>Status: {report.status}</Text>
          <Text style={styles.paragraph}>Grant: {report.grant.grant_name}</Text>
          <Text style={styles.paragraph}>
            Organization: {report.organization.organization_name}
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.heading}>Questionnaire Responses</Text>
          {report.question_answers.map((qa, i) => (
            <View key={qa.id} style={styles.qa}>
              <Text style={styles.label}>
                {i + 1}. {qa.question}
              </Text>
              <Text style={styles.paragraph}>{qa.answer}</Text>
            </View>
          ))}
        </View>
      </Page>
    </Document>
  );
}
