"use client";

import { Document, Page, Text, View, StyleSheet } from "@react-pdf/renderer";
import {
  ImpactType,
  OutcomeTypeWithImpact,
  OutputType,
  ActivityType,
} from "@/types/quantitative";
import {
  quarterKeyPairs,
  getQuarterTotal,
  calculateOutputProgress,
} from "@/app/grantmaker/reports/_lib/quantitative";

const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontSize: 11,
    fontFamily: "Helvetica",
  },
  section: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 16,
    marginBottom: 10,
    fontWeight: "bold",
  },
  subheading: {
    fontSize: 14,
    marginBottom: 6,
    fontWeight: "bold",
  },
  text: {
    marginBottom: 4,
  },
  label: {
    fontWeight: "bold",
  },
  table: {
    display: "table",
    width: "auto",
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#ccc",
    borderStyle: "solid",
  },
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#ddd",
    borderBottomStyle: "solid",
  },
  tableHeader: {
    backgroundColor: "#f0f0f0",
    fontWeight: "bold",
  },
  cell: {
    flex: 1,
    padding: 4,
    borderRightWidth: 1,
    borderRightColor: "#ddd",
  },
  lastCell: {
    borderRightWidth: 0,
  },
});

type Props = {
  impact: ImpactType;
  outcomes: OutcomeTypeWithImpact[];
};

export function ExportQuantitativeReportPDF({ impact, outcomes }: Props) {
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.heading}>Impact Report</Text>

        <View style={styles.section}>
          <Text style={styles.text}>
            <Text style={styles.label}>Impact Statement: </Text>
            {impact.impact_statement}
          </Text>
          <Text style={styles.text}>
            <Text style={styles.label}>Year:</Text> {impact.year}
          </Text>
          {impact.pre_intervention_assessment && (
            <Text style={styles.text}>
              <Text style={styles.label}>Pre-Intervention Assessment: </Text>
              {impact.pre_intervention_assessment}
            </Text>
          )}
          {impact.post_intervention_assessment && (
            <Text style={styles.text}>
              <Text style={styles.label}>Post-Intervention Assessment: </Text>
              {impact.post_intervention_assessment}
            </Text>
          )}
          {impact.tools_used && (
            <Text style={styles.text}>
              <Text style={styles.label}>Tools Used:</Text> {impact.tools_used}
            </Text>
          )}
        </View>

        {outcomes.map((outcome, i) => (
          <View key={outcome.id} style={styles.section}>
            <Text style={styles.subheading}>Outcome {i + 1}</Text>
            <Text style={styles.text}>{outcome.outcome_statement}</Text>

            {outcome.outputs.map((output, j) => {
              const totalPlanned = getQuarterTotal(
                output,
                quarterKeyPairs.map((q) => q.plan),
              );
              const totalActual = getQuarterTotal(
                output,
                quarterKeyPairs.map((q) => q.actual),
              );
              const progress = calculateOutputProgress(output).toFixed(1);

              return (
                <View key={output.id} style={styles.section}>
                  <Text style={styles.label}>Output {j + 1}</Text>
                  <Text style={styles.text}>{output.description}</Text>
                  <Text style={styles.text}>
                    <Text style={styles.label}>Unit:</Text> {output.unit}
                  </Text>
                  <Text style={styles.text}>
                    <Text style={styles.label}>Progress:</Text> {progress}%
                  </Text>

                  <View style={styles.table}>
                    {/* Table Header */}
                    <View style={[styles.tableRow, styles.tableHeader]}>
                      <Text style={styles.cell}>Quarter</Text>
                      <Text style={styles.cell}>Planned</Text>
                      <Text style={[styles.cell]}>Actual</Text>
                      <Text style={[styles.cell, styles.lastCell]}>
                        Remarks
                      </Text>
                    </View>

                    {/* Table Rows for Q1–Q4 */}
                    {quarterKeyPairs.map((q) => (
                      <View key={q.quarter} style={styles.tableRow}>
                        <Text style={styles.cell}>{q.quarter}</Text>
                        <Text style={styles.cell}>{output[q.plan] ?? "-"}</Text>
                        <Text style={styles.cell}>
                          {output[q.actual] ?? "-"}
                        </Text>
                        <Text style={[styles.cell, styles.lastCell]}>
                          {output[q.remark] ?? "-"}
                        </Text>
                      </View>
                    ))}

                    {/* Total Row */}
                    <View style={styles.tableRow}>
                      <Text style={styles.cell}>Total</Text>
                      <Text style={styles.cell}>{totalPlanned ?? "-"}</Text>
                      <Text style={styles.cell}>{totalActual ?? "-"}</Text>
                      <Text style={[styles.cell, styles.lastCell]}></Text>
                    </View>
                  </View>

                  {/* Activities */}
                  {output.activities.map((activity, k) => {
                    const totalPlanned = getQuarterTotal(
                      output,
                      quarterKeyPairs.map((q) => q.plan),
                    );
                    const totalActual = getQuarterTotal(
                      output,
                      quarterKeyPairs.map((q) => q.actual),
                    );
                    const progress = calculateOutputProgress(output).toFixed(1);

                    return (
                      <View key={activity.id} style={{ marginLeft: 8 }}>
                        <Text style={styles.label}>Activity {k + 1}</Text>
                        <Text style={styles.text}>{activity.description}</Text>
                        <Text style={styles.text}>
                          <Text style={styles.label}>Unit:</Text>{" "}
                          {activity.unit}
                        </Text>
                        <Text style={styles.text}>
                          <Text style={styles.label}>Progress:</Text> {progress}
                          %
                        </Text>

                        {/* Table for Activity quarters */}
                        <View style={styles.table}>
                          <View style={[styles.tableRow, styles.tableHeader]}>
                            <Text style={styles.cell}>Quarter</Text>
                            <Text style={styles.cell}>Planned</Text>
                            <Text style={styles.cell}>Actual</Text>
                            <Text style={[styles.cell, styles.lastCell]}>
                              Remarks
                            </Text>
                          </View>

                          {quarterKeyPairs.map((q) => (
                            <View key={q.quarter} style={styles.tableRow}>
                              <Text style={styles.cell}>{q.quarter}</Text>
                              <Text style={styles.cell}>
                                {activity[q.plan] ?? "-"}
                              </Text>
                              <Text style={styles.cell}>
                                {activity[q.actual] ?? "-"}
                              </Text>
                              <Text style={[styles.cell, styles.lastCell]}>
                                {activity[q.remark] ?? "-"}
                              </Text>
                            </View>
                          ))}

                          <View style={styles.tableRow}>
                            <Text style={styles.cell}>Total</Text>
                            <Text style={styles.cell}>
                              {totalPlanned ?? "-"}
                            </Text>
                            <Text style={styles.cell}>
                              {totalActual ?? "-"}
                            </Text>
                            <Text style={[styles.cell, styles.lastCell]}></Text>
                          </View>
                        </View>

                        {activity.remarks && (
                          <Text style={styles.text}>
                            <Text style={styles.label}>Remarks:</Text>{" "}
                            {activity.remarks}
                          </Text>
                        )}
                      </View>
                    );
                  })}
                </View>
              );
            })}
          </View>
        ))}
      </Page>
    </Document>
  );
}
