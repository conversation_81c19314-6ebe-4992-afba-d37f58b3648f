import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { updateProfile, transformOrganizationAPIResponseToOrganization, transformBasicDetailsFormToOrganizationAPIRequestPayload, transformKMPAPIResponseToKMP, transformKMPToKMPUpdateRequest, addKMP, transformHistoricalGrantFormTypeToHistoricalGrantAPIPayload, addHistoricalGrant, transformHistoricalGrantAPIResponseToHistoricalGrant } from "@/services/profile-service";
import { ApplicantDetailsProps, KMP } from "@/types/profile";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage
} from "@/components/ui/form";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Phone, Building, FileCheck, ScrollText, Plus } from "lucide-react";
import { motion } from "framer-motion";
import { ProfilePicUploader } from "./ProfilePicUploader";
import { getPresignedUrlForProfileLogo, uploadFileToS3 } from "@/lib/s3Uploader";
import { basicDetailsFormDefaultValues, BasicDetailsFormType, basicDetailsSchema } from "@/schemas/BasicDetailsSchema";
import { Button } from "../ui/button";
import { KMPList } from "./KMPList";
import { KMPAddModal } from "./KMPAddModal";
import { extractErrors } from "@/lib/axios-error-utils";
import { HistoricalGrantsList } from "./HistoricalGrantsList";
import { HistoricalGrantAddForm } from "./AddHistoricalGrant";
import { HistoricalGrantFormType } from "@/schemas/AddHistoricalGrantSchema";

export function BasicDetails({ organization }: ApplicantDetailsProps) {
  // New state for toggling edit mode for the whole page
  const [editing, setEditing] = useState(false);
  const [organizationData, setOrganizationData] = useState(organization);
  const [showAddKMPModal, setShowAddKMPModal] = useState(false);
  const [kmps, setKmps] = useState(organization?.kmps || []);
  const [showAddGrantModal, setShowAddGrantModal] = useState(false);
  const [historialGrants, setHistoricalGrants] = useState(organization?.previousGrants || []);

  // Initialize form with proper default values
  const form = useForm<BasicDetailsFormType>({
    resolver: zodResolver(basicDetailsSchema),
    defaultValues: basicDetailsFormDefaultValues,
    mode: "onChange",
  });

  // Helper function to safely get string values (converts undefined/null to empty string)
  const safeString = (value: any): string => value || "";

  // Update organization data when prop changes
  useEffect(() => {
    if (organization) {
      setOrganizationData(organization);
    }
  }, [organization]);

  // Reset form values when organizationData changes
  useEffect(() => {
    if (organizationData) {
      const formValues = {
        pointOfContactName: safeString(organizationData.pointOfContactName),
        phone: safeString(organizationData.phoneNumber),
        email: safeString(organizationData.emailAddress),
        addressLine1: safeString(organizationData.address?.addressLine1),
        addressLine2: safeString(organizationData.address?.addressLine2),
        locality: safeString(organizationData.address?.locality),
        city: safeString(organizationData.address?.city),
        state: safeString(organizationData.address?.state),
        postalCode: safeString(organizationData.address?.postalCode),
        country: safeString(organizationData.address?.country),
        organizationName: safeString(organizationData.organizationName),
        organizationLegalType: safeString(organizationData.organizationLegalType),
        csrRegistrationNumber: safeString(organizationData.csrRegistrationNumber),
        taxRegistrationNumber: safeString(organizationData.taxRegistrationNumber),
        taxRegistrationNumberUnder12A: safeString(organizationData.taxRegistrationNumberUnder12A),
        fcraRegistrationNumber: safeString(organizationData.fcraRegistrationNumber),
        trustRegistrationNumber: safeString(organizationData.trustRegistrationNumber),
        darpanId: safeString(organizationData.darpanId),
        panNumber: safeString(organizationData.panNumber),
        mission: safeString(organizationData.mission),
        vision: safeString(organizationData.vision),
        backgroundHistory: safeString(organizationData.backgroundHistory),
      };

      form.reset(formValues);
    }
  }, [organizationData, form]);

  const uploadProfileLogo = async (file: File) => {
    const { upload_url: uploadUrl, file_url: fileUrl, object_key: objectKey } = await getPresignedUrlForProfileLogo(file);
    try {
      const response = await uploadFileToS3(file, uploadUrl);
      console.log("File uploaded to url: " + uploadUrl + " successfully, response = " + JSON.stringify(response));
    } catch (error) {
      console.error("Error uploading file:", error);
      toast.error("Failed to upload profile logo");
      return;
    }

    const updateProfileLogoPayload = {
      "logo_key": objectKey
    }
    const response = updateProfile(updateProfileLogoPayload);
    console.log("Profile logo updated successfully, response = " + JSON.stringify(response));
  }

  const handleSubmit = async (values: BasicDetailsFormType) => {
    try {
      setEditing(false);
      const transformedData = transformBasicDetailsFormToOrganizationAPIRequestPayload(values);
      const response = await updateProfile(transformedData);

      if (response.status === "SUCCESS") {
        setOrganizationData(transformOrganizationAPIResponseToOrganization(response.data));
        toast.success("Profile updated successfully!");
      } else {
        toast.error("Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("An error occurred while saving.");
    }
  };

  const handleCancel = () => {
    // Reset form to current organization data when canceling
    if (organizationData) {
      const formValues = {
        pointOfContactName: safeString(organizationData.pointOfContactName),
        phone: safeString(organizationData.phoneNumber),
        email: safeString(organizationData.emailAddress),
        addressLine1: safeString(organizationData.address?.addressLine1),
        addressLine2: safeString(organizationData.address?.addressLine2),
        locality: safeString(organizationData.address?.locality),
        city: safeString(organizationData.address?.city),
        state: safeString(organizationData.address?.state),
        postalCode: safeString(organizationData.address?.postalCode),
        country: safeString(organizationData.address?.country),
        organizationName: safeString(organizationData.organizationName),
        organizationLegalType: safeString(organizationData.organizationLegalType),
        csrRegistrationNumber: safeString(organizationData.csrRegistrationNumber),
        taxRegistrationNumber: safeString(organizationData.taxRegistrationNumber),
        taxRegistrationNumberUnder12A: safeString(organizationData.taxRegistrationNumberUnder12A),
        fcraRegistrationNumber: safeString(organizationData.fcraRegistrationNumber),
        trustRegistrationNumber: safeString(organizationData.trustRegistrationNumber),
        darpanId: safeString(organizationData.darpanId),
        panNumber: safeString(organizationData.panNumber),
        mission: safeString(organizationData.mission),
        vision: safeString(organizationData.vision),
        backgroundHistory: safeString(organizationData.backgroundHistory),
      };
      form.reset(formValues);
    }
    setEditing(false);
  };

  async function handleKMPAdd(data: Partial<KMP>): Promise<void> {
    setShowAddKMPModal(false);
    // Do NOT setEditing(false) here; we don't want to toggle editing state on KMP add.
    const payload = transformKMPToKMPUpdateRequest(data)
    try {
      const response = await addKMP(payload);
      console.log("Response = ", JSON.stringify(response))
      if (response.status === "SUCCESS") {
        const newKmp = transformKMPAPIResponseToKMP(response.data);
        setKmps((prev) => [newKmp, ...(prev || [])]);
        toast.success("KMP added successfully");
      }
      // Do NOT call updateProfile or show unrelated toasts here.
    } catch (error) {
      const errorMessages = extractErrors(error);
      errorMessages.forEach((msg) => toast.error(msg));
    }
  }

  const handleAddHistoricalGrant = async (values: HistoricalGrantFormType) => {
    const backendData = transformHistoricalGrantFormTypeToHistoricalGrantAPIPayload(values);
    try {
      const response = await addHistoricalGrant(backendData);
      console.log("Response = ", JSON.stringify(response));
      if (response.status === 'SUCCESS') {
        console.log("Success")
        const historicalGrant = transformHistoricalGrantAPIResponseToHistoricalGrant(response.data);
        setHistoricalGrants((prev) => [historicalGrant, ...(prev || [])]);
        toast.success("Historical Grant added successfully!");
      }
      // Do NOT call updateProfile or show unrelated toasts here.
    } catch (error) {
      toast.error("Unable to add historical grant!")
    }
  }

  return (
    <div>
      {showAddKMPModal && (
        <KMPAddModal
          onSave={(data) => handleKMPAdd(data)}
          onClose={() => setShowAddKMPModal(false)}
        />
      )}
      {showAddGrantModal && (
        <HistoricalGrantAddForm isOpen={showAddGrantModal} onClose={() => setShowAddGrantModal(false)} onSubmit={handleAddHistoricalGrant} />
      )}
      <ProfilePicUploader imageSrc={organizationData?.logoKey} onChange={uploadProfileLogo} />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          {/* Edit button at the top right */}
          <div className="flex justify-end mb-4">
            {editing ? (
              <>
                <button
                  type="button"
                  className="bg-red-400 text-white mr-2 px-4 py-2 rounded-md hover:bg-red-600 transition"
                  onClick={handleCancel}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-[#00998F] text-white px-4 py-2 rounded-md hover:bg-teal-600 transition"
                >
                  Save
                </button>
              </>
            ) : (
              <button
                type="button"
                className="bg-[#00998F] text-white px-4 py-2 rounded-md hover:bg-teal-600 transition"
                onClick={() => setEditing(true)}
              >
                Edit
              </button>
            )}
          </div>

          <motion.div
            key="contact-info-content"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
          >
            <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
              <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
              <CardHeader className="pb-2">
                <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                  <Phone className="h-5 w-5 mr-2 text-[#00998F]" />
                  Contact Information
                </CardTitle>
                <CardDescription>Contact details of the organization</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Name</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="pointOfContactName"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.pointOfContactName}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Phone Number</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Phone" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.phoneNumber}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Email</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Email" {...field}/>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.emailAddress}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Address Line 1</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="addressLine1"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Address Line 1" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.address?.addressLine1}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Address Line 2</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="addressLine2"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Address Line 2" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.address?.addressLine2}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Locality</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="locality"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Locality" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.address?.locality}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">City</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="City" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.address?.city}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">State</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="state"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="State" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.address?.state}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Postal Code</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="postalCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Postal Code" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.address?.postalCode}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Country</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="country"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Country" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.address?.country}</p>
                    )}
                  </div>
                </div>
                <div className="py-6">
                  <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                    <Building className="h-5 w-5 mr-2 text-[#00998F]" />
                    Organization Information
                  </CardTitle>
                  <CardDescription>Basic details about the organization</CardDescription>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Organization Name</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="organizationName"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Org Name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.organizationName}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Organization Type</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="organizationLegalType"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Type" {...field} disabled />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.organizationLegalTypeName}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Darpan ID</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="darpanId"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Darpan ID" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.darpanId}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">PAN Number</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="panNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="PAN" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.panNumber}</p>
                    )}
                  </div>
                  {/* Mission */}
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Mission</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="mission"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Mission" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.mission}</p>
                    )}
                  </div>
                  {/* Vision */}
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Vision</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="vision"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Vision" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.vision}</p>
                    )}
                  </div>
                </div>
                {/* Background and History field, full width, after the grid */}
                <div className="mt-6">
                  <h3 className="text-sm font-medium text-gray-500">Background and History</h3>
                  {editing ? (
                    <FormField
                      control={form.control}
                      name="backgroundHistory"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea placeholder="Background and History" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ) : (
                    <p className="mt-1 text-gray-900 font-medium whitespace-pre-line">{organizationData?.backgroundHistory}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            key="organization-info-content"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4, delay: 0.1 }}
            className="mt-6"
          >
          </motion.div>

          <motion.div
            key="grant-history-content"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4, delay: 0.3 }}
            className="mt-6"
          >
            <div className="">
              <div className="flex justify-end mb-4">
                <Button type="button" onClick={() => setShowAddKMPModal(true)} className="bg-[#00998F] text-white hover:bg-teal-600">
                  <Plus className="w-4 h-4 mr-2" />
                  Add KMP
                </Button>
              </div>
              <KMPList kmps={kmps} />
            </div>

            <div className="mt-8">
              <div className="flex justify-end mb-4">
                <Button type="button" onClick={() => setShowAddGrantModal(true)} className="bg-[#00998F] text-white hover:bg-teal-600">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Grant
                </Button>
              </div>
              <HistoricalGrantsList historicalGrants={historialGrants} />
            </div>
          </motion.div>
        </form>
      </Form>
    </div>
  );
}