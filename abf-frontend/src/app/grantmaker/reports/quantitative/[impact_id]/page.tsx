"use client";

import Layout from "@/components/grantmaker/Layout";
import { PencilIcon, PlusIcon, Undo2Icon } from "lucide-react";
import { Button, buttonVariants } from "@/components/ui/button";
import Link from "next/link";
import { notFound, useParams, useRouter } from "next/navigation";
import { impact, outcomes } from "@/data/quantitative";
import { ImpactHeader } from "@/components/reports/quantitative/Impacts";
import { OutcomesList } from "@/components/reports/quantitative/Outcomes";
import { motion } from "framer-motion";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  getGrantmakerImpact,
  getGrantmakerOutcomesForImpact,
} from "@/services/grantmaker/quantitative-service";
import {
  ImpactType,
  ImpactTypeWithOrganization,
  OutcomeType,
  OutcomeTypeWithImpact,
} from "@/types/quantitative";
import { Skeleton } from "@/components/ui/skeleton";
import {
  AddImpactType,
  EditImpactButton,
} from "@/components/grantmaker/QuantitativeReports";
import apiClient from "@/lib/apiClient";

export default function QuantitativeReportImpactPage() {
  const params = useParams();
  const id: number = Number(params.impact_id);
  const router = useRouter();

  const impactQuery = useQuery<ImpactTypeWithOrganization | undefined, Error>({
    queryKey: ["impacts", id],
    queryFn: async () => {
      const impacts = await getGrantmakerImpact(id.toString());
      return impacts;
    },
  });

  const outcomesQuery = useQuery<OutcomeTypeWithImpact[], Error>({
    queryKey: ["outcomes", id],
    queryFn: async () => {
      const outcomes = await getGrantmakerOutcomesForImpact(id.toString());
      return outcomes;
    },
  });

  if (!impactQuery.isLoading && !impactQuery.data) {
    notFound();
  }

  const editImpact = useMutation<unknown, unknown, AddImpactType>({
    mutationKey: ["impacts", id],
    mutationFn: async (impact: AddImpactType, granteeId: number) => {
      try {
        const response = await apiClient.patch(
          `/api/reports/grantmaker/quantitative/impacts/${id}/`,
          {
            organization_id: granteeId,
            ...impact,
          },
        );

        impactQuery.refetch();

        return response;
      } catch (error: any) {
        const errorData = error.response?.data;
        if (errorData?.non_field_errors?.[0]) {
          throw new Error(errorData.non_field_errors[0]);
        }
        const firstError = errorData ? Object.values(errorData)[0] : null;
        throw new Error(
          Array.isArray(firstError) ? firstError[0] : "Failed to submit report",
        );
      }
    },
  });

  return (
    <Layout title="Quantitative Reports">
      <div className="flex items-center justify-between mb-2">
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -10, opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Button variant="ghost" onClick={() => router.back()}>
            <Undo2Icon />
            Back
          </Button>
        </motion.div>

        {impactQuery.data && (
          <motion.div
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -10, opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <EditImpactButton
              impact={impactQuery.data}
              editImpact={editImpact}
            />
          </motion.div>
        )}
      </div>

      {impactQuery.isLoading && <Skeleton className="h-[200px]" />}
      {impactQuery.data && (
        <ImpactHeader
          impact={impactQuery.data}
          outcomes={outcomesQuery.data ?? []}
        />
      )}

      <div className="space-y-4 pt-8">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Outcomes ({outcomesQuery.data?.length ?? 0})
          </h2>

          <Link
            href={`/grantmaker/reports/quantitative/${id}/outcome/new`}
            className={buttonVariants()}
          >
            <PlusIcon />
            New Outcome
          </Link>
        </div>

        {outcomesQuery.data && (
          <OutcomesList
            outcomes={outcomesQuery.data || []}
            userType={"GRANT_MAKER"}
          />
        )}
      </div>
    </Layout>
  );
}
