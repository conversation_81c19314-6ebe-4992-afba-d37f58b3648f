"use client";

import Layout from "@/app/layout";
import { ExportQuantitativeReportPDF } from "@/components/reports/quantitative/export-quantitave-report";
import { ImpactHeader } from "@/components/reports/quantitative/Impacts";
import { OutcomesList } from "@/components/reports/quantitative/Outcomes";
import { Button, buttonVariants } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { impact, outcomes } from "@/data/quantitative";
import { cn } from "@/lib/utils";
import {
  getGranteeImpact,
  getGranteeOutcomesForImpact,
} from "@/services/grantmaker/quantitative-service";
import { ImpactType, OutcomeTypeWithImpact } from "@/types/quantitative";
import { PDFDownloadLink } from "@react-pdf/renderer";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { DownloadIcon, Loader2Icon, PlusIcon, Undo2Icon } from "lucide-react";
import Link from "next/link";
import { notFound, useParams, useRouter } from "next/navigation";
import { Fragment } from "react";

export default function() {
  const params = useParams();
  const id: number = Number(params.impact_id);
  const router = useRouter();

  const impactQuery = useQuery<ImpactType | undefined, Error>({
    queryKey: ["impact", id],
    queryFn: async () => {
      const impacts = await getGranteeImpact(id.toString());
      return impacts;
    },
  });

  const outcomesQuery = useQuery<OutcomeTypeWithImpact[], Error>({
    queryKey: ["outcomes", id],
    queryFn: async () => {
      const outcomes = await getGranteeOutcomesForImpact(id.toString());
      return outcomes;
    },
  });

  if (!impactQuery.isLoading && !impactQuery.data) {
    notFound();
  }

  return (
    <>
      <div className="mb-2 flex justify-between items-center">
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -10, opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Button variant="ghost" onClick={() => router.back()}>
            <Undo2Icon />
            Back
          </Button>
        </motion.div>

        {impactQuery.data && outcomesQuery.data && (
          <motion.div
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -10, opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <PDFDownloadLink
              className={cn(
                buttonVariants(),
                "bg-teal-500 hover:bg-teal-500/80 duration-200",
              )}
              document={
                <ExportQuantitativeReportPDF
                  impact={impactQuery.data}
                  outcomes={outcomesQuery.data}
                />
              }
              fileName={`impact-report-${impactQuery.data?.id}.pdf`}
            >
              {({ loading }) =>
                loading ? (
                  <>
                    <Loader2Icon className="size-4 animate-spin" /> Loading
                  </>
                ) : (
                  <>
                    <DownloadIcon className="size-4" /> Export
                  </>
                )
              }
            </PDFDownloadLink>
          </motion.div>
        )}
      </div>
      {impactQuery.isLoading && <Skeleton className="h-[200px]" />}
      {impactQuery.data && (
        <ImpactHeader
          impact={impactQuery.data}
          outcomes={outcomesQuery.data ?? []}
        />
      )}
      <div className="space-y-4 pt-8">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Outcomes ({outcomesQuery.data?.length ?? 0})
          </h2>
        </div>

        {outcomesQuery.data && (
          <OutcomesList outcomes={outcomesQuery.data} userType={"GRANTEE"} />
        )}
      </div>
    </>
  );
}
